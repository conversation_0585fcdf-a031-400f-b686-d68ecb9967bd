<script setup lang="ts">
import { ref, onMounted, reactive, nextTick } from 'vue'
import {
  Button,
  Card,
  Toast,
  Dialog,
  Field,
  Form,
  showSuccessToast,
  showFailToast,
  Overlay,
  showConfirmDialog,
  showToast,
} from 'vant'
import 'vant/lib/index.css'
import {
  getRecipes,
  addRecipe,
  updateRecipe,
  deleteRecipes,
  type Channel,
  type Recipe,
} from '@/api/recipe'
import { makeMilkTea, pollMakeTeaResult } from '@/api/send' // 修改：引入封装方法
import { keyboardZh } from '@/components/virtual-keyboard'
import { getChannels, type channelRes } from '@/api/channel'
import { useRouter } from 'vue-router'

// 将TeaCard类型改为使用Recipe接口
type TeaCard = Recipe

// 初始化数据
const teaCards = ref<TeaCard[]>([])
const isMakingTea = ref(false) // 新增：出茶中状态
const show = ref(false) // 修改：初始为false
const value = ref('') // 当前输入框的值
const currentChannelIndex = ref<number | null>(null) // 当前编辑的通道索引
const showDelete = ref(false) // 控制是否显示删除按钮
const allChannels = ref<channelRes[]>([]) // 控制是否显示删除按钮

// 获取茶品数据
const fetchTeaCards = async () => {
  try {
    teaCards.value = await getRecipes()
    console.log('teaCards', teaCards.value)
  } catch (error) {
    showFailToast('获取数据失败')
  }
}
// 获取通道数据
const getChannel = async () => {
  const res = await getChannels()
  console.log('res', res)
  allChannels.value = res
}
getChannel()

// 添加新卡片
const addCard = async () => {
  try {
    // 根据allChannels生成通道数据
    const channels = allChannels.value.map((ch) => ({
      channel_id: ch.id,
      amount: 0,
    }))
    const newCard = { name: `新茶${teaCards.value.length + 1}`, channels }
    console.log('newCard', newCard)

    await addRecipe(newCard)
    await fetchTeaCards()
  } catch (error) {
    console.error('添加失败', error)
    showFailToast('添加失败')
  }
}

// 出茶操作
const makeTea = async (name: string) => {
  if (isMakingTea.value) return
  const res = await showConfirmDialog({
    title: name,
    message: '是否出茶？',
    width: '60%',
  }).catch(() => {
    // 取消操作
    console.log('取消出茶')
    showToast('取消出茶')
  })
  console.log('res', res)
  if (!res) return

  isMakingTea.value = true
  try {
    const card = teaCards.value.find((c) => c.name === name)
    if (!card) {
      showFailToast('未找到该茶品')
      isMakingTea.value = false
      return
    }
    const modes = ['Normal', 'Combine', 'Suck_back']
    // 适配对象数组结构
    const pump_list = card.channels
      .map((ch, idx) => ({
        pump_id: String(ch.channel_id ?? 101 + idx),
        weight: `${ch.amount}`,
        mode: modes[Math.floor(Math.random() * modes.length)],
      }))
      .filter((item) => Number(item.weight.replace('g', '')) > 0)
    if (pump_list.length === 0) {
      showFailToast('请设置至少一个通道重量')
      isMakingTea.value = false
      return
    }
    await makeMilkTea(pump_list)
    showSuccessToast(`正在出茶：${name}`)
    const ok = await pollMakeTeaResult()
    // if (ok) {
    //   showSuccessToast('出茶完成')
    // } else {
    //   showFailToast('出茶超时')
    // }
  } catch (error) {
    showFailToast('出茶失败')
  }
  isMakingTea.value = false
}

// 删除卡片
const deleteCard = async (card: TeaCard) => {
  await showConfirmDialog({
    title: '删除确认',
    message: `确定要删除「${card.name}」吗？`,
    width: '60%',
  })
    .then(async () => {
      try {
        await deleteRecipes(card)
        showSuccessToast('删除成功')
        await fetchTeaCards()
        showDelete.value = false // 删除后自动关闭删除模式（可选）
      } catch (error) {
        showFailToast('删除失败')
      }
    })
    .catch(() => {
      // 取消
      showDelete.value = false // 取消后自动关闭删除模式（可选）
    })
}

// 编辑表单相关
const showEditDialog = ref(false)
const showKeyboardZh = ref(false)
// 初始化为对象数组
const currentCard = reactive<TeaCard>({
  id: 0,
  name: '',
  channels: Array.from({ length: 8 }, (_, i) => ({
    channel_id: i + 1,
    amount: 0,
  })),
})

// 打开编辑表单
const openEditDialog = (card: TeaCard) => {
  // 直接获取最新的数据
  const latestCard = teaCards.value.find((item) => item.id === card.id)
  if (latestCard) {
    // 深拷贝，避免响应式污染
    Object.assign(currentCard, {
      ...latestCard,
      channels: latestCard.channels.map((ch) => ({ ...ch })),
    })
    showEditDialog.value = true
    nextTick(() => {
      setTimeout(() => {
        showKeyboardZh.value = true
      }, 100)
    })
  }
}

// 保存表单数据
const saveEdit = async () => {
  try {
    //把amount 转为数字
    currentCard.channels.forEach((channel) => {
      channel.amount = Number(channel.amount)
    })
    console.log('currentCard', currentCard)

    await updateRecipe(currentCard)
    await fetchTeaCards()
    showSuccessToast('保存成功')
  } catch (error) {
    console.error('保存失败', error)
    showFailToast('保存失败')
  }
  showEditDialog.value = false
}

// 页面加载时获取数据
onMounted(() => {
  fetchTeaCards()
})

// 自定义指令：长按
const vLongpress = {
  mounted(el: HTMLElement, binding: any) {
    let pressTimer: null = null

    const start = (e: Event) => {
      if (e.type === 'click' && e.button !== 0) return
      if (pressTimer === null) {
        pressTimer = setTimeout(() => {
          binding.value()
        }, 800) // 长按时间为 800ms
      }
    }

    const cancel = () => {
      if (pressTimer !== null) {
        clearTimeout(pressTimer)
        pressTimer = null
      }
    }

    el.addEventListener('mousedown', start)
    el.addEventListener('touchstart', start)
    el.addEventListener('click', cancel)
    el.addEventListener('mouseout', cancel)
    el.addEventListener('touchend', cancel)
    el.addEventListener('touchcancel', cancel)
  },
}
// 自定义指令：长按
const vsmLongpress = {
  mounted(el: HTMLElement, binding: any) {
    let pressTimer: null = null

    const start = (e: Event) => {
      if (e.type === 'click' && e.button !== 0) return
      if (pressTimer === null) {
        pressTimer = setTimeout(() => {
          binding.value()
        }, 800) // 长按时间为 800ms
      }
    }

    const cancel = () => {
      if (pressTimer !== null) {
        clearTimeout(pressTimer)
        pressTimer = null
      }
    }

    el.addEventListener('mousedown', start)
    el.addEventListener('touchstart', start)
    el.addEventListener('click', cancel)
    el.addEventListener('mouseout', cancel)
    el.addEventListener('touchend', cancel)
    el.addEventListener('touchcancel', cancel)
  },
}

// 处理数字键盘输入
const onInput = (val: string) => {
  console.log('val', val)

  if (currentChannelIndex.value !== null) {
    let oldVal = String(currentCard.channels[currentChannelIndex.value]?.amount ?? '')
    // 防止多余的0
    if (oldVal === '0') oldVal = ''
    // 限制小数点只出现一次
    if (val === '.' && oldVal.includes('.')) return
    // 不允许以小数点开头
    if (val === '.' && oldVal === '') return
    const newVal = oldVal + val
    console.log('newVal', newVal)

    // 允许小数点
    currentCard.channels[currentChannelIndex.value].amount = Number(newVal)
  }
}
const onDelete = () => {
  if (currentChannelIndex.value !== null) {
    let oldVal = String(currentCard.channels[currentChannelIndex.value]?.amount ?? '')
    oldVal = oldVal.slice(0, -1)
    currentCard.channels[currentChannelIndex.value].amount = Number(oldVal) || 0
  }
}
const onFocusField = (index: number) => {
  currentChannelIndex.value = index
  show.value = true
}
const onKeyboardBlur = () => {
  show.value = false
  currentChannelIndex.value = null
}
const valueZH = ref('')

//点击键盘的值
const clickKey = (key) => {
  console.log('key-->>', key)
}
//点击键盘时数字的值
const clickNumber = (key) => {
  // console.log("key-->>",key);
}
const manyDict = ref('dict/chowder.json')
const singleDict = ref('dict/baseDict.json')

// 连续点击计数及定时器
const router = useRouter()
const adminClickCount = ref(0)
let adminClickTimer: ReturnType<typeof setTimeout> | null = null

const handleAdminClick = () => {
  adminClickCount.value++
  if (adminClickTimer) clearTimeout(adminClickTimer)
  adminClickTimer = setTimeout(() => {
    adminClickCount.value = 0
  }, 3000) // 3秒内需连续点击5次
  if (adminClickCount.value >= 5) {
    adminClickCount.value = 0
    router.push('/admin')
  }
}
</script>

<template>
  <div class="tea-container">
    <!-- 新增：右上角隐藏点击区域 -->
    <div class="admin-entry" @click="handleAdminClick"></div>
    <van-overlay :show="isMakingTea" z-index="2000">
      <!-- <div class="overlay-content" v-if="isMakingTea">
        <span>正在出茶，请稍候...</span>
      </div> -->
      <van-loading vertical class="overlay-content">
        <template #icon>
          <van-icon name="setting-o" size="30" />
        </template>
        正在出茶，请稍候...
      </van-loading>
    </van-overlay>
    <div class="card-grid">
      <div v-for="card in teaCards" :key="card.id" class="tea-card">
        <div>
          <div style="height: 50px; font-size: 20px; text-align: center; color: black">
            {{ card.name }}
          </div>
        </div>
        <van-button
          round
          type="primary"
          :disabled="isMakingTea"
          @click="makeTea(card.name)"
          style="width: 60%"
          v-longpress="() => openEditDialog(card)"
          >出 茶</van-button
        >
        <van-button
          v-if="showDelete"
          round
          type="danger"
          style="margin-top: 8px; width: 60%"
          :disabled="isMakingTea"
          @click="deleteCard(card)"
          >删除</van-button
        >
      </div>
    </div>
    <div class="add-btn-wrapper">
      <Button type="success" round @click="addCard">添加卡片</Button>
      <Button type="danger" round @click="showDelete = !showDelete">
        {{ showDelete ? '取消删除' : '删除卡片' }}
      </Button>
    </div>
    <!-- 编辑表单弹窗 -->
    <Dialog
      v-model:show="showEditDialog"
      show-cancel-button
      width="800px"
      title="编辑茶品"
      @confirm="saveEdit"
      @cancel="fetchTeaCards()"
      @close="showKeyboardZh = false"
      destroy-on-close
    >
      <div style="height: 400px; overflow-y: auto; padding: 30px">
        <Form label-width="13em">
          <Field v-model="currentCard.name" label="名称" placeholder="请输入茶品名称" required>
            <template #input>
              <input
                keyboard="true"
                v-model="currentCard.name"
                placeholder="请输入茶品名称"
                type="text"
                style="border: none"
              />
            </template>
          </Field>
          <Field
            v-for="(channel, index) in currentCard.channels"
            :key="index"
            class="form-item"
            v-model="currentCard.channels[index].amount"
            :label="'通道' + (channel.channel_id ?? index + 1) + '目标重量(克)'"
            type="number"
            placeholder="目标重量(克)"
            @focus="onFocusField(index)"
            readonly
            clickable
          />
        </Form>
      </div>
    </Dialog>
    <van-number-keyboard
      :show="show"
      theme="custom"
      extra-key="."
      z-index="9999"
      close-button-text="完成"
      @blur="onKeyboardBlur"
      @input="onInput"
      @delete="onDelete"
    />

    <div
      v-if="showKeyboardZh"
      class="scale-content"
      :style="{
        transform: `scale(0.85)`,
        width: '1200px',
        height: '100px',
        transformOrigin: 'center bottom',
        translate: '-9%',
      }"
    >
      <keyboardZh
        :transitionTime="'0.5s'"
        :maxQuantify="8"
        @clickKey="clickKey"
        float
        :manyDict="manyDict"
        :singleDict="singleDict"
        @clickNumber="clickNumber"
        :blurHide="true"
      ></keyboardZh>
    </div>
  </div>
</template>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
}
.scale-content {
  position: absolute;
  bottom: 0px;
  /* left: 0px; */
  z-index: 9999;
}
.tea-container {
  padding: 20px;
  box-sizing: border-box;
  height: 100vh;
  width: 100vw;
  overflow-y: auto;
  background-color: #00ddff;
}

/* 新增遮罩内容样式 */
.overlay-content {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 20px;
  text-align: center;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.tea-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px;
}

.add-btn-wrapper {
  margin-top: 16px;
  text-align: center;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.form-item {
  margin-bottom: 12px;
}

.form-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.admin-entry {
  position: fixed;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  z-index: 99999;
  /* 可选：设置透明或低透明度 */
  background: transparent;
  /* pointer-events: auto; */
}
</style>
