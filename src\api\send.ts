import { showFailToast, showSuccessToast } from 'vant'
import request from '../utils/axios'

/**
 * 通用发送命令接口
 * @param data 请求体对象，结构需符合后端要求
 * @returns 后端响应
 */
export const sendCommand = (data: any) => {
  return request.post('/send', data)
}

export interface makeMilkTeaReq {
  device: string
  command: string
  pump_list: PumpList[]
}

export interface PumpList {
  pump_id: string
  weight: string
  mode: string //mode  = 'Normal','Combine','Suck_back',3种出货模式
}
/**
 * 出茶操作
 * @param pump_list 泵参数列表
 * @returns Promise<void>
 */
export const makeMilkTea = async (pump_list: PumpList[]) => {
  const req: makeMilkTeaReq = {
    device: 'robo',
    command: 'make_one_milk_tea',
    pump_list,
  }
  return sendCommand({ req })
}

/**
 * 轮询出茶结果
 * @param maxTries 最大轮询次数
 * @param interval 间隔(ms)
 * @returns Promise<boolean> 是否完成
 */
export const pollMakeTeaResult = async (maxTries = 30, interval = 2000): Promise<boolean> => {
  let tries = 0
  while (tries < maxTries) {
    try {
      const res = await sendCommand({
        req: {
          device: 'robo',
          command: 'poll',
        },
      })
      // 修改判断条件，兼容后端返回格式
      console.log(res)

      if (res?.rep?.result === 'success') {
        showSuccessToast('出茶完成')
        return true
      } else if (res?.rep?.result === 'error') {
        showFailToast(res?.rep?.desc || '操作失败')
        // 失败处理
        return false
      }
    } catch (e) {
      // 忽略单次异常
    }
    await new Promise((resolve) => setTimeout(resolve, interval))
    tries++
  }
  return false
}
