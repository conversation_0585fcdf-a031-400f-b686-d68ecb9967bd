<template>
  <div class="flex flex-col items-center min-h-screen bg-gray-100 pt-6">
    <van-button icon="cross" size="small" @click="goBack" plain />
    <h2 class="text-2xl font-bold mb-6 mt-6">管理员页面</h2>
    <van-button type="primary" @click="showChannelFunc" class="mb-6">通道管理</van-button>
    <van-popup v-model:show="showChannel" position="bottom" :style="{ height: '90%' }">
      <div class="p-4 bg-white rounded-t-xl h-full flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">通道管理</h2>
          <van-button icon="cross" size="small" @click="showChannel = false" plain />
        </div>
        <van-form @submit="onAddChannel" class="mb-4 flex flex-wrap gap-2">
          <van-field label="通道名称" required class="flex-1 min-w-[160px]">
            <template #input>
              <input
                keyboard="true"
                v-model="newChannel.name"
                placeholder="请输入通道名称"
                type="text"
                style="border: none"
              /> </template
          ></van-field>
          <van-field
            v-model="newChannel.channel_no"
            label="通道编号"
            placeholder="请输入通道编号"
            required
            class="flex-1 min-w-[160px]"
            readonly
            clickable
            @click="onFocusField(newChannel, 'channel_no', 'text', $event)"
          />
          <van-field
            v-model.number="newChannel.max_capacity"
            label="最大容量"
            placeholder="请输入最大容量"
            required
            class="flex-1 min-w-[160px]"
            readonly
            clickable
            @click="onFocusField(newChannel, 'max_capacity', 'number', $event)"
          />
          <van-field
            v-model.number="newChannel.warn_capacity"
            label="警告容量"
            placeholder="请输入警告容量"
            required
            class="flex-1 min-w-[160px]"
            readonly
            clickable
            @click="onFocusField(newChannel, 'warn_capacity', 'number', $event)"
          />
          <van-field
            v-model="newChannel.mode_name"
            label="模式"
            required
            class="flex-1 min-w-[160px]"
            readonly
            clickable
            @click="showModePicker = true"
            placeholder="请选择模式"
          />
          <van-popup v-model:show="showModePicker" position="bottom">
            <van-picker
              :columns="modeOptions"
              @confirm="onModeConfirm"
              @cancel="showModePicker = false"
            />
          </van-popup>
          <div class="w-full flex justify-end mt-2">
            <van-button round type="success" native-type="submit">新增通道</van-button>
          </div>
        </van-form>
        <div class="overflow-x-auto flex-1">
          <table
            class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-fixed"
          >
            <thead>
              <tr class="bg-gray-50">
                <th class="py-2 px-3 text-center w-24">名称</th>
                <th class="py-2 px-3 text-center w-24">编号</th>
                <th class="py-2 px-3 text-center w-46">最大容量</th>
                <th class="py-2 px-3 text-center w-46">警告容量</th>
                <th class="py-2 px-3 text-center w-46">出货模式</th>
                <th class="py-2 px-3 text-center w-46">解决操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="channel in channels" :key="channel.id" class="border-t">
                <td class="py-1 px-2 text-center">
                  <van-field
                    v-show="editId === channel.id"
                    v-model="editChannel.name"
                    placeholder="名称"
                    class="w-full"
                    input-align="center"
                  >
                    <template #input>
                      <input
                        class="w-full text-center"
                        keyboard="true"
                        v-model="editChannel.name"
                        placeholder="请输入通道名称"
                        type="text"
                        style="border: none"
                      /> </template
                  ></van-field>
                  <span v-show="editId !== channel.id">{{ channel.name }}</span>
                </td>
                <td class="py-1 px-2 text-center">
                  <van-field
                    v-if="editId === channel.id"
                    v-model="editChannel.channel_no"
                    placeholder="编号"
                    class="w-full"
                    readonly
                    clickable
                    input-align="center"
                    @click="onFocusField(editChannel, 'channel_no', 'text', $event)"
                  />
                  <span v-else>{{ channel.channel_no }}</span>
                </td>
                <td class="py-1 px-2 text-center">
                  <van-field
                    v-if="editId === channel.id"
                    v-model.number="editChannel.max_capacity"
                    placeholder="最大容量"
                    class="w-full"
                    readonly
                    input-align="center"
                    clickable
                    @click="onFocusField(editChannel, 'max_capacity', 'number', $event)"
                  />
                  <span v-else>{{ channel.max_capacity }}</span>
                </td>
                <td class="py-1 px-2 text-center">
                  <van-field
                    v-if="editId === channel.id"
                    v-model.number="editChannel.warn_capacity"
                    placeholder="警告容量"
                    class="w-full"
                    readonly
                    input-align="center"
                    clickable
                    @click="onFocusField(editChannel, 'warn_capacity', 'number', $event)"
                  />
                  <span v-else>{{ channel.warn_capacity }}</span>
                </td>
                <td class="py-1 px-2 text-center">
                  <template v-if="editId === channel.id">
                    <!-- <van-field
                      v-model="editChannel.mode_name"
                      :value="
                        modeOptions.find((option) => option.name === editChannel.mode_name)?.value
                      "
                      placeholder="模式"
                      class="w-20"
                      readonly
                      clickable
                      @click="showEditModePicker = true"
                    /> -->
                    <van-popup v-model:show="showEditModePicker" position="bottom">
                      <van-picker
                        :columns="modeOptions"
                        @confirm="onEditModeConfirm"
                        @cancel="showEditModePicker = false"
                      />
                    </van-popup>
                  </template>
                  <div class="w-full text-center" @click="showEditModePicker = true">
                    {{ modeOptions.find((option) => option.value === channel.mode)?.text }}
                  </div>
                </td>
                <td class="py-1 px-2 text-center">
                  <div v-if="editId !== channel.id" class="flex gap-1 justify-center">
                    <van-button size="small" type="primary" @click="onEdit(channel)"
                      >编辑</van-button
                    >
                    <van-button size="small" type="danger" @click="onDeleteChannel(channel)"
                      >删除</van-button
                    >
                  </div>
                  <div v-else class="flex gap-1 justify-center">
                    <van-button size="small" type="success" @click="onUpdateChannel"
                      >保存</van-button
                    >
                    <van-button size="small" @click="cancelEdit">取消</van-button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </van-popup>
    <!-- 可在此添加更多管理功能 -->
    <van-number-keyboard
      :show="show"
      theme="custom"
      extra-key="."
      z-index="9999"
      close-button-text="完成"
      @blur="onKeyboardBlur"
      @input="onInput"
      @delete="onDelete"
      @show="onKeyboardShow"
      @hide="onKeyboardHide"
    />
    <div
      v-if="showKeyboardZh"
      class="scale-content"
      :style="{
        transform: `scale(0.85)`,
        width: '1210px',
        height: '10px',
        transformOrigin: 'center bottom',
        // translate: '-9%',
      }"
    >
      <keyboardZh
        :transitionTime="'0.5s'"
        :maxQuantify="8"
        @clickKey="clickKey"
        float
        :manyDict="manyDict"
        :singleDict="singleDict"
        @clickNumber="clickNumber"
        :blurHide="true"
      ></keyboardZh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  getChannels,
  addChannel,
  deleteChannel,
  updateChannel,
  channelRes,
  channelReq,
} from '../api/channel'
import { showConfirmDialog, showToast } from 'vant'
import router from '@/router'
import { keyboardZh } from '@/components/virtual-keyboard'

const show = ref(false) // 修改：初始为false
const showChannel = ref(false)
const channels = ref<channelRes[]>([])
const newChannel = reactive<Omit<channelReq, 'id'>>({
  channel_no: '',
  max_capacity: 0,
  mode: '',
  mode_name: '',
  name: '',
  warn_capacity: 0,
})

const editId = ref<number | null>(null)
const editChannel = reactive<Partial<channelRes>>({})
const showKeyboardZh = ref(false)
const manyDict = ref('dict/chowder.json')
const singleDict = ref('dict/baseDict.json')
// 数字键盘相关状态
const currentFieldRef = ref<any>(null)
const currentFieldType = ref<string>('')
const currentFieldKey = ref<string>('')
const currentFieldElement = ref<HTMLElement | null>(null)
const originalScrollTop = ref<number>(0)

const fetchChannels = async () => {
  channels.value = await getChannels()
}

const showChannelFunc = () => {
  showChannel.value = true
  showKeyboardZhFunc()
}

const showKeyboardZhFunc = () => {
  // nextTick(() => {
  //   showKeyboardZh.value = false
  nextTick(() => {
    showKeyboardZh.value = true
  })
  // })
}

const onAddChannel = async () => {
  await addChannel({ ...newChannel })
  Object.assign(newChannel, {
    channel_no: '',
    max_capacity: 0,
    mode: '',
    mode_name: '',
    name: '',
    warn_capacity: 0,
  })
  await fetchChannels()
  showToast({ message: '新增成功' })
}

const onDeleteChannel = async (channel: channelRes) => {
  showConfirmDialog({
    title: '确认删除',
    message: `确定要删除通道「${channel.name}」吗？`,
  })
    .then(async () => {
      await deleteChannel(channel)
      await fetchChannels()
      showToast({ message: '删除成功' })
    })
    .catch(() => {})
}

const onEdit = (channel: channelRes) => {
  editId.value = channel.id
  Object.assign(editChannel, channel)
}

const onUpdateChannel = async () => {
  if (editId.value !== null) {
    await updateChannel(editChannel as channelRes)
    editId.value = null
    await fetchChannels()
    showToast({ message: '修改成功' })
  }
}

const cancelEdit = () => {
  editId.value = null
}

const modeOptions = [
  { text: '普通', value: 'Normal' },
  { text: '合并', value: 'Combine' },
  { text: '回吸', value: 'Suck_back' },
]
const showModePicker = ref(false)
const showEditModePicker = ref(false)
const onModeConfirm = ({ selectedValues, selectedOptions }) => {
  newChannel.mode = selectedValues[0]
  newChannel.mode_name = selectedOptions[0].text
  showModePicker.value = false
}

const onEditModeConfirm = ({ selectedValues, selectedOptions }) => {
  console.log(selectedValues)

  editChannel.mode = selectedValues[0]
  editChannel.mode_name = selectedOptions[0].text
  showEditModePicker.value = false
}

// 数字键盘事件处理
const onInput = (val: string) => {
  if (!currentFieldRef.value || !currentFieldKey.value) return

  const currentValue = String(currentFieldRef.value[currentFieldKey.value] || '')

  // 防止多余的0
  const oldVal = currentValue === '0' ? '' : currentValue

  // 限制小数点只出现一次
  if (val === '.' && oldVal.includes('.')) return

  // 不允许以小数点开头
  if (val === '.' && oldVal === '') return

  const newVal = oldVal + val
  currentFieldRef.value[currentFieldKey.value] =
    currentFieldType.value === 'number' ? Number(newVal) : newVal
}

const onDelete = () => {
  if (!currentFieldRef.value || !currentFieldKey.value) return

  const currentValue = String(currentFieldRef.value[currentFieldKey.value] || '')
  const newVal = currentValue.slice(0, -1)
  currentFieldRef.value[currentFieldKey.value] =
    currentFieldType.value === 'number' ? Number(newVal) || 0 : newVal
}

// 滚动到输入框位置，防止 van-number-keyboard 遮挡
const scrollToField = (element: HTMLElement) => {
  if (!element) return

  // 获取元素位置信息
  const rect = element.getBoundingClientRect()

  // van-number-keyboard 的实际高度（根据主题和配置调整）
  const keyboardHeight = 260 // van-number-keyboard 的典型高度
  const viewportHeight = window.innerHeight
  const safeMargin = 20 // 安全边距

  // 如果输入框在弹窗内，优先滚动弹窗内容
  const popup = element.closest('.van-popup')
  if (popup) {
    const popupScrollContainer = popup.querySelector('.overflow-x-auto')
    if (popupScrollContainer) {
      const containerRect = popupScrollContainer.getBoundingClientRect()
      const elementRect = element.getBoundingClientRect()

      // 计算输入框底部到容器底部的距离
      const distanceToBottom = containerRect.bottom - elementRect.bottom

      // 如果距离小于键盘高度加安全边距，需要滚动
      if (distanceToBottom < keyboardHeight + safeMargin) {
        const scrollAmount = keyboardHeight + safeMargin - distanceToBottom
        const newScrollTop = popupScrollContainer.scrollTop + scrollAmount

        popupScrollContainer.scrollTo({
          top: newScrollTop,
          behavior: 'smooth',
        })
        return
      }
    }
  }

  // 如果不在弹窗内或弹窗滚动不够，滚动整个页面
  const elementBottom = rect.bottom
  const availableSpace = viewportHeight - keyboardHeight - safeMargin

  if (elementBottom > availableSpace) {
    const scrollAmount = elementBottom - availableSpace
    const newScrollTop = window.pageYOffset + scrollAmount

    window.scrollTo({
      top: newScrollTop,
      behavior: 'smooth',
    })
  }
}

// 恢复原始滚动位置
const restoreScroll = () => {
  window.scrollTo({
    top: originalScrollTop.value,
    behavior: 'smooth',
  })
}

// van-number-keyboard 显示时的处理
const onKeyboardShow = () => {
  // 添加键盘激活状态
  document.body.classList.add('keyboard-active')

  // 延迟滚动，确保键盘完全显示
  setTimeout(() => {
    if (currentFieldElement.value) {
      scrollToField(currentFieldElement.value)
    }
  }, 300)
}

// van-number-keyboard 隐藏时的处理
const onKeyboardHide = () => {
  // 移除键盘激活状态
  document.body.classList.remove('keyboard-active')

  // 恢复原始滚动位置
  setTimeout(() => {
    restoreScroll()
  }, 100)
}

const onKeyboardBlur = () => {
  show.value = false
  currentFieldRef.value = null
  currentFieldType.value = ''
  currentFieldKey.value = ''
  currentFieldElement.value = null
}

const onFocusField = (
  fieldRef: any,
  fieldKey: string,
  fieldType: string = 'text',
  event?: Event,
) => {
  // 保存原始滚动位置
  originalScrollTop.value = window.pageYOffset || document.documentElement.scrollTop

  currentFieldRef.value = fieldRef
  currentFieldKey.value = fieldKey
  currentFieldType.value = fieldType

  // 立即获取目标元素
  let targetElement: HTMLElement | null = null

  if (event && event.target) {
    targetElement = (event.target as HTMLElement).closest('.van-field') as HTMLElement
  }

  // 如果没有事件目标，尝试查找当前聚焦的输入框
  if (!targetElement) {
    // 延迟查找，等待DOM更新
    nextTick(() => {
      const focusedField = document.querySelector('.van-field--focus') as HTMLElement
      if (focusedField) {
        currentFieldElement.value = focusedField
      }
    })
  } else {
    currentFieldElement.value = targetElement
  }

  // 显示键盘（这会触发 onKeyboardShow）
  show.value = true
}

const goBack = () => {
  router.back()
}

//点击键盘的值
const clickKey = (key) => {
  console.log('key-->>', key)
}
//点击键盘时数字的值
const clickNumber = (key) => {
  // console.log("key-->>",key);
}
onMounted(() => {
  fetchChannels()
})
</script>
<style scoped>
.scale-content {
  position: absolute;
  bottom: 0px;
  z-index: 9999;
}

/* 确保表格列宽固定，防止编辑时变形 */
.table-fixed {
  table-layout: fixed;
}

/* 确保van-field在表格中的样式一致 */
.table-fixed td .van-field {
  width: 100% !important;
}

.table-fixed td .van-field__control {
  text-align: center;
}

/* 键盘弹起时的页面适配 */
.keyboard-active {
  transition: transform 0.3s ease-in-out;
}

/* van-number-keyboard 弹起时的页面调整 */
.keyboard-active .van-popup {
  transition: transform 0.3s ease-in-out;
}

/* 确保弹窗内容可以平滑滚动 */
.overflow-x-auto {
  scroll-behavior: smooth;
}

/* 为聚焦的输入框添加高亮效果 */
.van-field--focus {
  background-color: rgba(25, 137, 250, 0.05);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

/* van-number-keyboard 样式优化 */
:deep(.van-number-keyboard) {
  border-top: 1px solid #ebedf0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* 确保键盘不会影响页面布局 */
:deep(.van-number-keyboard--show) {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
